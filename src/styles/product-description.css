/* Product Description Formatting Styles */

.formatted-content-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Aria<PERSON>, sans-serif;
}

.formatted-content h2 {
  color: #1f2937 !important;
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
  margin-top: 0 !important;
  padding-bottom: 0.5rem !important;
  border-bottom: 2px solid #e5e7eb !important;
}

.formatted-content h3 {
  color: #374151 !important;
  font-size: 1.2rem !important;
  font-weight: 600 !important;
  margin-top: 2rem !important;
  margin-bottom: 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.formatted-content p {
  color: #4b5563 !important;
  line-height: 1.7 !important;
  margin-bottom: 1rem !important;
  font-size: 0.95rem !important;
}

.formatted-content .intro {
  font-size: 1.1rem !important;
  color: #374151 !important;
  font-weight: 500 !important;
  margin-bottom: 1.5rem !important;
  padding: 1rem !important;
  background-color: #f8fafc !important;
  border-radius: 0.5rem !important;
  border-left: 4px solid #3b82f6 !important;
}

.formatted-content ul {
  margin: 1rem 0 !important;
  padding-left: 0 !important;
  list-style: none !important;
}

.formatted-content li {
  color: #4b5563 !important;
  margin-bottom: 0.75rem !important;
  line-height: 1.6 !important;
  padding: 0.75rem !important;
  background-color: #f0f9ff !important;
  border-radius: 0.5rem !important;
  border-left: 4px solid #3b82f6 !important;
  font-size: 0.9rem !important;
}

.formatted-content .features-list li {
  background-color: #eff6ff !important;
  border-left-color: #2563eb !important;
}

.formatted-content .improvement-list li {
  background-color: #ecfdf5 !important;
  border-left-color: #10b981 !important;
}

.formatted-content .guarantee-section {
  background-color: #f3f4f6 !important;
  padding: 1.5rem !important;
  border-radius: 0.75rem !important;
  margin-top: 2rem !important;
  border: 2px solid #e5e7eb !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

.formatted-content .guarantee-section p {
  margin-bottom: 0.75rem !important;
  font-size: 0.9rem !important;
  color: #374151 !important;
}

.formatted-content .guarantee-section strong {
  color: #1f2937 !important;
  font-weight: 600 !important;
}

.formatted-content strong {
  color: #1f2937 !important;
  font-weight: 600 !important;
}

/* Dark mode styles */
.dark .formatted-content h2 {
  color: #f9fafb !important;
  border-bottom-color: #4b5563 !important;
}

.dark .formatted-content h3 {
  color: #e5e7eb !important;
}

.dark .formatted-content p {
  color: #d1d5db !important;
}

.dark .formatted-content .intro {
  color: #e5e7eb !important;
  background-color: #374151 !important;
  border-left-color: #60a5fa !important;
}

.dark .formatted-content li {
  color: #d1d5db !important;
  background-color: #1e3a8a !important;
  border-left-color: #60a5fa !important;
}

.dark .formatted-content .features-list li {
  background-color: #1e3a8a !important;
  border-left-color: #3b82f6 !important;
}

.dark .formatted-content .improvement-list li {
  background-color: #064e3b !important;
  border-left-color: #34d399 !important;
  color: #d1fae5 !important;
}

.dark .formatted-content .guarantee-section {
  background-color: #374151 !important;
  border-color: #4b5563 !important;
}

.dark .formatted-content .guarantee-section p {
  color: #d1d5db !important;
}

.dark .formatted-content .guarantee-section strong {
  color: #f9fafb !important;
}

.dark .formatted-content strong {
  color: #f9fafb !important;
}

/* Simple content styling */
.simple-content {
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.95rem;
}

.dark .simple-content {
  color: #d1d5db;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .formatted-content h2 {
    font-size: 1.3rem !important;
  }
  
  .formatted-content h3 {
    font-size: 1.1rem !important;
  }
  
  .formatted-content .intro {
    padding: 0.75rem !important;
    font-size: 1rem !important;
  }
  
  .formatted-content li {
    padding: 0.5rem !important;
    font-size: 0.85rem !important;
  }
  
  .formatted-content .guarantee-section {
    padding: 1rem !important;
  }
}
